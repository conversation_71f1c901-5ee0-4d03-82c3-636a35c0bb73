<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--区域数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="areaName"
            placeholder="请输入区域名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="areaOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            default-expand-all
            highlight-current
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--设备数据-->
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="设备类型" prop="deviceTypeId">
            <el-select
              v-model="queryParams.deviceTypeId"
              placeholder="请选择"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in deviceTypeQueryOptions"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="设备名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入设备名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="设备编号" prop="number">
            <el-input
              v-model="queryParams.number"
              placeholder="请输入设备编号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="在线状态" prop="onlineStatus">
            <el-select
              v-model="queryParams.onlineStatus"
              placeholder="在线状态"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="dict in dict.type.online_status"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['biz:device:add']"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['biz:device:edit']"
            >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['biz:device:remove']"
            >删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['biz:device:export']"
            >导出
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center"/>
          <el-table-column label="设备名称" align="center" prop="name"/>
          <el-table-column label="设备编号" align="center" prop="number"/>
          <el-table-column label="设备类型" align="center" prop="deviceTypeName"/>
          <el-table-column label="大类" align="center" prop="broadType">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.device_broad_type" :value="scope.row.broadType"/>
            </template>
          </el-table-column>
          <el-table-column label="在线状态" align="center" prop="onlineStatus">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.online_status" :value="scope.row.onlineStatus"/>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['biz:device:edit']"
              >修改
              </el-button>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['biz:device:remove']"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改设备对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="大类" prop="broadType">
          <el-select
            v-model="form.broadType"
            placeholder="请选择"
            style="width: 100%"
            @change="broadTypeChange"
          >
            <el-option
              v-for="dict in dict.type.device_broad_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="设备类型" prop="deviceTypeId">
          <el-select
            v-model="form.deviceTypeId"
            placeholder="设备类型"
            style="width: 100%"
          >
            <el-option
              v-for="dict in deviceTypeOptions"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="设备名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入设备名称"/>
        </el-form-item>
        <el-form-item label="设备编号" prop="number">
          <el-input v-model="form.number" placeholder="请输入设备编号"/>
        </el-form-item>
        <el-form-item label="区域" prop="areaId">
          <el-cascader :options="areaOptions" v-model="form.areaId" :props="props"
                       filterable style="width: 100%"
          >
          </el-cascader>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listDevice, getDevice, delDevice, addDevice, updateDevice} from "@/api/biz/device";
import {deviceTypeOption} from "@/api/biz/deviceType";
import {listArea} from "@/api/biz/area";

export default {
  name: "Device",
  dicts: ['online_status', 'device_broad_type'],
  data() {
    return {
      // 区域名称
      areaName: undefined,
      areaOptions: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      props: {
        emitPath: false,
        value: 'id', // 绑定的值字段名
        label: 'name', // 显示的文字字段名
        children: 'children' // 子选项字段名
      },

      // 项目id
      projectId: localStorage.getItem('projectId'),
      // 类型选项
      deviceTypeOptions: [],
      // 查询选项
      deviceTypeQueryOptions: [],
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备表格数据
      deviceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceTypeId: undefined,
        broadType: undefined,
        name: undefined,
        number: undefined,
        onlineStatus: undefined,
        areaId: undefined,
        projectId: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        broadType: [
          {required: true, message: "设备大类不能为空", trigger: "blur"}
        ],
        deviceTypeId: [
          {required: true, message: "设备类型不能为空", trigger: "blur"}
        ],
        name: [
          {required: true, message: "设备名称不能为空", trigger: "blur"}
        ],
        number: [
          {required: true, message: "设备编号不能为空", trigger: "blur"}
        ],
        areaId: [
          { required: true, message: '区域不能为空', trigger: 'blur' }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getDeviceTypeOption();
    this.getAreaTree();
  },
  watch: {
    // 根据名称筛选部门树
    areaName(val) {
      this.$refs.tree.filter(val)
    }
  },
  methods: {
    /** 查询区域下拉树结构 */
    getAreaTree() {
      listArea().then(response => {
        this.areaOptions = response.data
      })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.areaId = data.id
      this.handleQuery()
    },

    broadTypeChange() {
      this.form.deviceTypeId = undefined
      this.getTypeByBroadType();
    },
    // 设备类型查询选项
    getDeviceTypeOption() {
      deviceTypeOption().then(res => {
        this.deviceTypeQueryOptions = res.data;
      })
    },
    // 设备类型选项
    getTypeByBroadType() {
      deviceTypeOption({
        broadType: this.form.broadType,
      }).then(res => {
        this.deviceTypeOptions = res.data;
      })
    },
    /** 查询设备列表 */
    getList() {
      this.loading = true;
      this.queryParams.projectId = this.projectId;
      listDevice(this.queryParams).then(response => {
        this.deviceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        deviceTypeId: undefined,
        broadType: undefined,
        name: undefined,
        number: undefined,
        onlineStatus: undefined,
        projectId: undefined,
        delFlag: undefined,
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.areaId = undefined
      this.$refs.tree.setCurrentKey(null)
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getDevice(id).then(response => {
        this.loading = false;
        this.form = response.data;
        this.getTypeByBroadType();
        this.open = true;
        this.title = "修改设备";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          this.form.projectId = this.projectId;
          if (this.form.id != null) {
            updateDevice(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          } else {
            addDevice(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).finally(() => {
              this.buttonLoading = false;
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除？').then(() => {
        this.loading = true;
        return delDevice(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('biz/device/export', {
        ...this.queryParams
      }, `device_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
