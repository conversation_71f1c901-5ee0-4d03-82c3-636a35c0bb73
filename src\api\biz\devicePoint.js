import request from '@/utils/request'

// 查询设备点位列表
export function listDevicePoint(query) {
  return request({
    url: '/biz/devicePoint/list',
    method: 'get',
    params: query
  })
}

// 查询设备点位详细
export function getDevicePoint(id) {
  return request({
    url: '/biz/devicePoint/' + id,
    method: 'get'
  })
}

// 新增设备点位
export function addDevicePoint(data) {
  return request({
    url: '/biz/devicePoint',
    method: 'post',
    data: data
  })
}

// 修改设备点位
export function updateDevicePoint(data) {
  return request({
    url: '/biz/devicePoint',
    method: 'put',
    data: data
  })
}

// 删除设备点位
export function delDevicePoint(id) {
  return request({
    url: '/biz/devicePoint/' + id,
    method: 'delete'
  })
}

// 查询设备点位选项
export function devicePointOption(query) {
  return request({
    url: '/biz/devicePoint/option',
    method: 'get',
    params: query
  })
}
